# Document
- LLMs do NOT write to this file, ONLY HUMANS
- this file is HIGH LEVEL enough to use most of it for other projects

# RULES
- decoupled modules where sensible
- easy-to-test is a top priority
- easy-to-debug is a top priority
- concise, short, relatively simple code is top priority

## Testing
- test resulst must be clearly logged to tests_log/ folder
- tests logs must contain enough information to show what the module in question is doing, IE. pass/fail is NOT enough!
- test_log_inspector.py (calls LLM) is used to inspect a test log file and rate responses, and catch more failure modes and bad tests
- browser testing: 
   pyenv activate agbase311 && export $(cat ./environment_sam_local | xargs) && python manage.py runserver 127.0.0.1:8000

## Levelization
- each layer adds single concern, can be tested without layers above (and sometimes, on its own)
- ultimately whole system can be tested at multiple levels and E2E


## LEVELS

### level 001: structure
- a new django app named ceto_chat in ./gaia/djangaia
- single views.py file which renders an html template called ceto_chat_base.html
- ceto_chat_base.html loads the Vue.js library
- ceto_chat_base.html loads a javascript file name ceto_app.js and a file named ceto_chat.css in the head section.  
- ceto_chat_base.html renders the html markup <h1>Ceto Chat</h1>

#### validate
- app and html page load in the browser at http://127.0.0.1:8000/ceto_chat/ and renders the html template without errors 


### level 002: debugability
- ceto_chat_base.html loads a file named debug.js which contains the logic for the in-page debug panel created in v1
- at this level the debug panel is not connected to any functionality, it is just a static panel that can be toggled on and off


#### validate
- app and html page load in the browser at http://127.0.0.1:8000/ceto_chat/ and renders the html template without errors 
- debug panel is visible and functional


### level 0031: connect to mcp server
- connect django app to the running mcp server
- access MCP from django project: gaia/djangaia django app: gaia/djangaia/gaiachat_v2 
- look at gaia/djangaia/gaiachat/ for vague inspiration (dont copy)
- use standard MCP protocol format for django MCP responses
- avoid seperate django and MCP processes

#### validate
- tool list should be visible in debug panel

### level 0033: seprate MCP API concerns
- abstract MCP API loging in djangaia/cet_chat to api_mcp.py
- this should be as simple as possible
- assume only a single MCP server for now and MCP server details should be placed in djangaia/settings
- views.py should import from api_mcp.py 

#### validate
- tool list should be visible in debug panel


### level 0034: logging from fontend to backend
- add a button to the html template which calls the test MCP tool echostring (temprorary - will be reomved in next higher levels)
- write response to an html div on the page (temprorary - will be reomved in next higher levels)
- request and response should be clearly visible in the debug panel

#### validate
- request and response should be clearly visible in the debug panel